import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AiAgentGobalManageService {
  // ========================= 全局共享属性 =========================
  private _isShowChat: boolean = false; // 是否显示聊天窗口
  private _generateModelFieldChange$: Subject<any> = new Subject<any>(); // 创建模型返回的数据(字段)
  private _generateModelBusinessChange$: Subject<any> = new Subject<any>(); // 创建模型返回的数据(业务对象)

  get isShowChat(): boolean {
    return this._isShowChat;
  }

  get generateModelFieldChange$(): Observable<any> {
    return this._generateModelFieldChange$.asObservable();
  }

  get generateModelBusinessChange$(): Observable<any> {
    return this._generateModelBusinessChange$.asObservable();
  }

  // ========================= 全局共享属性的操作 =========================

  setIsShowChat(value: boolean): void {
    this._isShowChat = value;
  }

  setGenerateFieldModel(value: any): void {
    this._generateModelFieldChange$.next(value);
  }

  setGenerateBusinessModel(value: any): void {
    this._generateModelBusinessChange$.next(value);
  }
}
