import { IAgentConfig } from './ai-agent-manage.type';

export const aiAgentList: IAgentConfig[] = [
  {
    agentId: '4',
    agentType: 'indepthAI',
    thirdId: 'a827ffb0-22cc-45de-8e81-d33ad3f1c6f9',
    agentName: 'AI架构师',
    lang: {
      agentName: {
        zh_CN: 'AI架构师',
        zh_TW: 'AI架构师',
        en_US: 'AI架构师',
      },
      agentDesc: {
        zh_CN: '我可以基于您的描述，智能生成应用，为了更好的了解您的需求，请参照模板进行需求描述',
        zh_TW: '我可以基于您的描述，智能生成应用，为了更好的了解您的需求，请参照模板进行需求描述',
        en_US: '我可以基于您的描述，智能生成应用，为了更好的了解您的需求，请参照模板进行需求描述',
      },
      welcomeMessage: {
        zh_CN: '👋🏻 Hi，娜娜AI架构师为您服务！',
        zh_TW: '👋🏻 Hi，娜娜AI架构师为您服务！',
        en_US: '👋🏻 Hi，娜娜AI架构师为您服务！',
      },
    },
    agentDesc: '我可以基于您的描述，智能生成应用，为了更好的了解您的需求，请参照模板进行需求描述',
    agentImageUrl: '',
    welcomeMessage: '👋🏻 Hi，娜娜AI架构师为您服务！',
    suggestionsList: [],
  },
  {
    agentId: '5',
    agentType: 'indepthAI',
    thirdId: '667ee76e-e2c9-43a1-a247-815895067c9f',
    agentName: '模型分析师',
    lang: {
      agentName: {
        zh_CN: '模型分析师',
        zh_TW: '模型分析师',
        en_US: '模型分析师',
      },
      agentDesc: {
        zh_CN: '模型分析师',
        zh_TW: '模型分析师',
        en_US: '模型分析师',
      },
      welcomeMessage: {
        zh_CN: '模型分析师',
        zh_TW: '模型分析师',
        en_US: '模型分析师',
      },
    },
    agentDesc: '模型分析师',
    agentImageUrl: '',
    welcomeMessage: '模型分析师',
    suggestionsList: [
      {
        name: '通过业务对象名称创建',
        description: '娜娜，请帮我设计出一份用于【商品管理】的模型数据结构',
        value: '娜娜，请帮我设计出一份用于【商品管理】的模型数据结构',
        lang: {
          name: {
            zh_CN: '通过业务对象名称创建',
            zh_TW: '通過業務對象名稱建立',
            en_US: 'Create by business object name',
          },
          description: {
            zh_CN: '娜娜，请帮我设计出一份用于【商品管理】的模型数据结构',
            zh_TW: '娜娜，請幫我設計出一份用於【商品管理】的模型數據結構',
            en_US: 'Nana, please help me design a model data structure for product management',
          },
          value: {
            zh_CN: '娜娜，请帮我设计出一份用于【商品管理】的模型数据结构',
            zh_TW: '娜娜，請幫我設計出一份用於【商品管理】的模型數據結構',
            en_US: 'Nana, please help me design a model data structure for product management',
          },
        },
      },
      {
        name: '通过业务对象描述创建',
        description: '娜娜，请帮我从描述中分析整理出可用模型数据结构',
        value:
          '订单日期、订单状态、商品总金额、运费、应付总额、订单备注、客户信息（客户姓名、联系电话、邮箱地址、送货地址）、支付信息（支付方式、支付状态） 、商品明细（商品名称、商品编号、数量、单价、小计）',
        lang: {
          name: {
            zh_CN: '通过业务对象描述创建',
            zh_TW: '通過業務對象描述建立',
            en_US: 'Create by business object description',
          },
          description: {
            zh_CN: '娜娜，请帮我从描述中分析整理出可用模型数据结构',
            zh_TW: '娜娜，請幫我從描述中分析整理出可用模型數據結構',
            en_US: 'Nana, please help me analyze and organize a usable model data structure from the description',
          },
          value: {
            zh_CN:
              '订单日期、订单状态、商品总金额、运费、应付总额、订单备注、客户信息（客户姓名、联系电话、邮箱地址、送货地址）、支付信息（支付方式、支付状态） 、商品明细（商品名称、商品编号、数量、单价、小计）',
            zh_TW:
              '訂單日期、訂單狀態、商品總金額、運費、應付總額、訂單備註、客戶信息（客戶姓名、聯繫電話、電子郵箱地址、送貨地址）、支付信息（支付方式、支付狀態） 、商品明細（商品名稱、商品編號、數量、單價、小計）',
            en_US:
              'Order date, order status, total amount of goods, freight, payable amount, order remarks, customer information (customer name, contact number, email address, delivery address), payment information (payment method, payment status), product details (product name, product number, quantity, unit price, subtotal)',
          },
        },
      },
      {
        name: '通过已有SQL创建',
        description: '娜娜，请帮我从SQL中分析整理出可用模型数据结构',
        value: `CREATE TABLE product ( id INT PRIMARY KEY AUTO_INCREMENT, product_name VARCHAR(255) NOT NULL COMMENT '商品名称', product_code VARCHAR(100) UNIQUE NOT NULL COMMENT '商品编码', category_id INT COMMENT '分类ID，可关联分类表', price DECIMAL(10,2) NOT NULL COMMENT '价格', stock INT DEFAULT 0 COMMENT '库存数量', status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-停用', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';`,
        lang: {
          name: {
            zh_CN: '通过已有SQL创建',
            zh_TW: '通過已有SQL建立',
            en_US: 'Create by existing SQL',
          },
          description: {
            zh_CN: '娜娜，请帮我从SQL中分析整理出可用模型数据结构',
            zh_TW: '娜娜，請幫我從SQL中分析整理出可用模型數據結構',
            en_US: 'Nana, please help me analyze and organize a usable model data structure from the SQL',
          },
          value: {
            zh_CN: `CREATE TABLE product ( id INT PRIMARY KEY AUTO_INCREMENT, product_name VARCHAR(255) NOT NULL COMMENT '商品名称', product_code VARCHAR(100) UNIQUE NOT NULL COMMENT '商品编码', category_id INT COMMENT '分类ID，可关联分类表', price DECIMAL(10,2) NOT NULL COMMENT '价格', stock INT DEFAULT 0 COMMENT '库存数量', status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-停用', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';`,
            zh_TW: `CREATE TABLE product ( id INT PRIMARY KEY AUTO_INCREMENT, product_name VARCHAR(255) NOT NULL COMMENT '商品名称', product_code VARCHAR(100) UNIQUE NOT NULL COMMENT '商品编码', category_id INT COMMENT '分类ID，可关联分类表', price DECIMAL(10,2) NOT NULL COMMENT '价格', stock INT DEFAULT 0 COMMENT '库存数量', status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-停用', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';`,
            en_US: `CREATE TABLE product ( id INT PRIMARY KEY AUTO_INCREMENT, product_name VARCHAR(255) NOT NULL COMMENT '商品名称', product_code VARCHAR(100) UNIQUE NOT NULL COMMENT '商品编码', category_id INT COMMENT '分类ID，可关联分类表', price DECIMAL(10,2) NOT NULL COMMENT '价格', stock INT DEFAULT 0 COMMENT '库存数量', status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-停用', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';`,
          },
        },
      },
    ],
  },
];

// 创建应用的mock数据
export const appCreateMockData = {
  appInfo: {
    description:
      '该系统涵盖员工基本信息管理（集中管理企业员工的详细个人信息及紧急联系人信息）、考勤管理（准确记录员工的出勤情况并进行统计分析）、薪资福利管理（管理员工的薪资和福利信息并进行计算）、培训发展管理（制定和管理员工的培训计划并记录参与情况）等领域。',
    lang: {
      description: {
        en_US:
          "This system covers areas such as employee basic information management (centrally managing detailed personal information and emergency contact information of enterprise employees), attendance management (accurately recording and statistically analyzing employees' attendance), salary and benefit management (managing and calculating employees' salary and benefit information), and training and development management (formulating and managing employees' training plans and recording participation).",
        zh_CN:
          '该系统涵盖员工基本信息管理（集中管理企业员工的详细个人信息及紧急联系人信息）、考勤管理（准确记录员工的出勤情况并进行统计分析）、薪资福利管理（管理员工的薪资和福利信息并进行计算）、培训发展管理（制定和管理员工的培训计划并记录参与情况）等领域。',
        zh_TW:
          '本系統涵蓋員工基本資訊管理（集中管理企業員工的詳細個人資訊及緊急聯繫人資訊）、考勤管理（準確記錄員工的出勤情況並進行統計分析）、薪資福利管理（管理員工的薪資和福利資訊並進行計算）、培訓發展管理（制定和管理員工的培訓計劃並記錄參與情況）等領域。',
      },
      name: {
        en_US: 'Employee Comprehensive Management System',
        zh_CN: '员工综合管理系统',
        zh_TW: '員工綜合管理系統',
      },
    },
    name: '员工综合管理系统',
  },
  businessInfo: [
    {
      code: 'employee',
      description: '集中管理企业员工的详细个人信息及紧急联系人信息',
      lang: {
        description: {
          en_US:
            'Centralized management of detailed personal information and emergency contact information of enterprise employees',
          zh_CN: '集中管理企业员工的详细个人信息及紧急联系人信息',
          zh_TW: '集中管理企業員工的詳細個人信息及緊急聯繫人信息',
        },
        name: {
          en_US: 'employee',
          zh_CN: '员工',
          zh_TW: '員工',
        },
      },
      model: {
        children: [
          {
            comment: '存储员工紧急联系人信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识紧急联系人的编号',
                fieldId: 'emergency_contact_id',
                fieldName: '紧急联系人编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '紧急联系人姓名',
                fieldId: 'contact_name',
                fieldName: '联系人姓名',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '紧急联系人与员工的关系',
                fieldId: 'relationship',
                fieldName: '与员工关系',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '紧急联系人联系电话',
                fieldId: 'contact_phone',
                fieldName: '联系电话',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
            ],
            name: 'emergency_contact',
          },
          {
            comment: '存储员工考勤记录信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识考勤记录的编号',
                fieldId: 'attendance_id',
                fieldName: '考勤记录编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '考勤日期',
                fieldId: 'attendance_date',
                fieldName: '考勤日期',
                fieldType: 'DATE',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '上班时间',
                fieldId: 'start_time',
                fieldName: '上班时间',
                fieldType: 'TIME',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '下班时间',
                fieldId: 'end_time',
                fieldName: '下班时间',
                fieldType: 'TIME',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '考勤状态',
                fieldId: 'attendance_status',
                fieldName: '考勤状态',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '出勤天数',
                fieldId: 'attendance_days',
                fieldName: '出勤天数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '迟到次数',
                fieldId: 'late_times',
                fieldName: '迟到次数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '早退次数',
                fieldId: 'early_leave_times',
                fieldName: '早退次数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '旷工天数',
                fieldId: 'absenteeism_days',
                fieldName: '旷工天数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
            ],
            name: 'attendance',
          },
          {
            comment: '存储员工薪资福利信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识薪资福利的编号',
                fieldId: 'salary_benefit_id',
                fieldName: '薪资福利编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工基本工资',
                fieldId: 'basic_salary',
                fieldName: '基本工资',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工绩效工资',
                fieldId: 'performance_salary',
                fieldName: '绩效工资',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工奖金',
                fieldId: 'bonus',
                fieldName: '奖金',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工补贴',
                fieldId: 'subsidy',
                fieldName: '补贴',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工社保缴纳情况',
                fieldId: 'social_security_status',
                fieldName: '社保缴纳情况',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工公积金缴纳情况',
                fieldId: 'provident_fund_status',
                fieldName: '公积金缴纳情况',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工带薪年假天数',
                fieldId: 'annual_leave_days',
                fieldName: '带薪年假天数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工应发工资',
                fieldId: 'should_pay_salary',
                fieldName: '应发工资',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工实发工资',
                fieldId: 'actual_pay_salary',
                fieldName: '实发工资',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
            ],
            name: 'salary_benefit',
          },
          {
            comment: '存储员工培训参与情况信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识培训参与情况的编号',
                fieldId: 'training_participation_id',
                fieldName: '培训参与编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与培训计划表中的培训计划编号关联的字段',
                fieldId: 'training_plan_id',
                fieldName: '培训计划编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工是否参加培训',
                fieldId: 'is_participated',
                fieldName: '是否参加培训',
                fieldType: 'BOOLEAN',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工培训成绩',
                fieldId: 'training_score',
                fieldName: '培训成绩',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
            ],
            name: 'training_participation',
          },
        ],
        comment: '集中管理企业员工的详细个人信息及紧急联系人信息',
        fields: [
          {
            fieldDescription: '唯一标识员工的编号',
            fieldId: 'employee_id',
            fieldName: '员工编号',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工姓名',
            fieldId: 'name',
            fieldName: '姓名',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工性别',
            fieldId: 'gender',
            fieldName: '性别',
            fieldType: 'VARCHAR',
            size: '10',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工出生日期',
            fieldId: 'birth_date',
            fieldName: '出生日期',
            fieldType: 'DATE',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工身份证号码',
            fieldId: 'id_card_number',
            fieldName: '身份证号',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工联系方式',
            fieldId: 'contact',
            fieldName: '联系方式',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工入职日期',
            fieldId: 'join_date',
            fieldName: '入职日期',
            fieldType: 'DATE',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工所在部门',
            fieldId: 'department',
            fieldName: '部门',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工职位',
            fieldId: 'position',
            fieldName: '职位',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工学历',
            fieldId: 'education',
            fieldName: '学历',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工毕业院校',
            fieldId: 'graduation_school',
            fieldName: '毕业院校',
            fieldType: 'VARCHAR',
            size: '50',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工在职状态',
            fieldId: 'status',
            fieldName: '在职状态',
            fieldType: 'VARCHAR',
            size: '10',
            type: 'SIMPLE',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'emergency_contact',
            },
            fieldId: 'emergency_contact',
            fieldName: '存储员工紧急联系人信息的子表',
            type: 'COLLECTION',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'attendance',
            },
            fieldId: 'attendance',
            fieldName: '存储员工考勤记录信息的子表',
            type: 'COLLECTION',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'salary_benefit',
            },
            fieldId: 'salary_benefit',
            fieldName: '存储员工薪资福利信息的子表',
            type: 'COLLECTION',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'training_participation',
            },
            fieldId: 'training_participation',
            fieldName: '存储员工培训参与情况信息的子表',
            type: 'COLLECTION',
          },
        ],
        name: 'employee',
      },
      name: 'employee',
    },
    {
      code: 'training_plan',
      description: '制定和管理员工的培训计划并记录参与情况',
      lang: {
        description: {
          en_US: 'Develop and manage employee training plans and record participation',
          zh_CN: '制定和管理员工的培训计划并记录参与情况',
          zh_TW: '制定和管理員工的培訓計劃並記錄參與情況',
        },
        name: {
          en_US: 'training_plan',
          zh_CN: '培训计划',
          zh_TW: '培訓計劃',
        },
      },
      model: {
        children: [
          {
            comment: '存储员工培训参与情况信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识培训参与情况的编号',
                fieldId: 'training_participation_id',
                fieldName: '培训参与编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的培训计划编号关联的字段',
                fieldId: 'training_plan_id',
                fieldName: '培训计划编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与员工表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工是否参加培训',
                fieldId: 'is_participated',
                fieldName: '是否参加培训',
                fieldType: 'BOOLEAN',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工培训成绩',
                fieldId: 'training_score',
                fieldName: '培训成绩',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
            ],
            name: 'training_participation',
          },
        ],
        comment: '制定和管理员工的培训计划并记录参与情况',
        fields: [
          {
            fieldDescription: '唯一标识培训计划的编号',
            fieldId: 'training_plan_id',
            fieldName: '培训计划编号',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '培训主题',
            fieldId: 'training_theme',
            fieldName: '培训主题',
            fieldType: 'VARCHAR',
            size: '50',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '培训时间',
            fieldId: 'training_time',
            fieldName: '培训时间',
            fieldType: 'DATETIME',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '培训地点',
            fieldId: 'training_location',
            fieldName: '培训地点',
            fieldType: 'VARCHAR',
            size: '50',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '培训讲师',
            fieldId: 'trainer',
            fieldName: '培训讲师',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'training_participation',
            },
            fieldId: 'training_participation',
            fieldName: '存储员工培训参与情况信息的子表',
            type: 'COLLECTION',
          },
        ],
        name: 'training_plan',
      },
      name: 'training_plan',
    },
  ],
};

export const modelCreateMockData: {
  msgId: string;
  scene: 'business' | 'field';
  content: any;
} = {
  msgId: '6ddf5571-eb04-4178-86d8-13f93d9a79ad',
  scene: 'business',
  content: {
    tableName: 'product_table',
    name: 'product_table',
    description: '存储商品信息的主表',
    lang: {
      name: {
        zh_TW: 'product_table',
        en_US: 'product_table',
        zh_CN: 'product_table',
      },
      description: {
        zh_TW: '存儲商品資訊的主表',
        en_US: '存儲商品資訊的主表',
        zh_CN: '存储商品信息的主表',
      },
    },
    dataList: [
      {
        fieldName: 'product_id',
        fieldDesc: '唯一标识商品的编号',
        fieldType: 'VARCHAR',
        fieldLength: '20',
        fieldPrecision: '',
        lang: {
          fieldDesc: {
            zh_TW: '唯一標識商品的編號',
            en_US: '唯一標識商品的編號',
            zh_CN: '唯一标识商品的编号',
          },
        },
        isSystem: false,
      },
      {
        fieldName: 'product_name',
        fieldDesc: '商品的名称',
        fieldType: 'VARCHAR',
        fieldLength: '50',
        fieldPrecision: '',
        lang: {
          fieldDesc: {
            zh_TW: '商品的名稱',
            en_US: '商品的名稱',
            zh_CN: '商品的名称',
          },
        },
        isSystem: false,
      },
      {
        fieldName: 'product_type',
        fieldDesc: '商品的类型',
        fieldType: 'VARCHAR',
        fieldLength: '50',
        fieldPrecision: '',
        lang: {
          fieldDesc: {
            zh_TW: '商品的類型',
            en_US: '商品的類型',
            zh_CN: '商品的类型',
          },
        },
        isSystem: false,
      },
      {
        fieldName: 'price',
        fieldDesc: '商品的单价',
        fieldType: 'DECIMAL',
        fieldLength: '10',
        fieldPrecision: '2',
        lang: {
          fieldDesc: {
            zh_TW: '商品的單價',
            en_US: '商品的單價',
            zh_CN: '商品的单价',
          },
        },
        isSystem: false,
      },
    ],
    leftCount: 199996,
    result:
      '{\n  "dataModel": {\n    "mainTable": {\n      "name": "product_table",\n      "englishName": "product_table",\n      "description": "存储商品信息的主表",\n      "fields": [\n        {\n          "fieldName": "商品编号",\n          "fieldEnglishName": "product_id",\n          "fieldDescription": "唯一标识商品的编号",\n          "fieldType": "VARCHAR",\n          "fieldLength":"20",\n          "fieldPrecision":""\n        },\n        {\n          "fieldName": "商品名称",\n          "fieldEnglishName": "product_name",\n          "fieldDescription": "商品的名称",\n          "fieldType": "VARCHAR",\n          "fieldLength":"50",\n          "fieldPrecision":""\n        },\n        {\n          "fieldName": "商品类型",\n          "fieldEnglishName": "product_type",\n          "fieldDescription": "商品的类型",\n          "fieldType": "VARCHAR",\n          "fieldLength":"50",\n          "fieldPrecision":""\n        },\n        {\n          "fieldName": "价格",\n          "fieldEnglishName": "price",\n          "fieldDescription": "商品的单价",\n          "fieldType": "DECIMAL",\n          "fieldLength":"10",\n          "fieldPrecision":"2"\n        }\n      ]\n    },\n    "subTables": []\n  }\n}',
  },
};
